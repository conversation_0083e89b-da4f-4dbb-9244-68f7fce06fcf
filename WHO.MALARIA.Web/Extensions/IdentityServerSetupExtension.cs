﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Models;
using static WHO.MALARIA.Domain.Constants.Constants;
using System.IdentityModel.Tokens.Jwt;
using WHO.MALARIA.Web.Areas.Idp.Configuration;
using WHO.MALARIA.Domain.Dtos;
using Microsoft.AspNetCore.Identity;
using WHO.MALARIA.Web.Areas.Idp.Helper;
using Duende.IdentityServer;
using WHO.MALARIA.Domain.Constants;
using System.Net;
using Microsoft.IdentityModel.Tokens;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using WHO.MALARIA.Web.Models;
using System.Text.RegularExpressions;
using Serilog;
using Newtonsoft.Json;
using System.Security.Claims;
using WHO.MALARIA.Web.Infrastructure.Events;
using Duende.IdentityServer.Configuration;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Web.Extensions
{
    internal static class IdentityServerSetupExtension
    {
        internal static IServiceCollection Setup(this IServiceCollection services)
        {

            //Custom claims factory to populate UserId
            services.AddScoped<IUserClaimsPrincipalFactory<IdentityDto>, CustomClaimsPrincipalFactory>();

            services.AddIdentityServer()
              .AddDeveloperSigningCredential()
              .AddInMemoryIdentityResources(IdentityServerConfig.GetIdentityResources())
              .AddInMemoryApiResources(IdentityServerConfig.GetApis())
              .AddAspNetIdentity<IdentityDto>()
               // Validate the clients from custom client store.
               .AddCustomUserStore();

            return services;
        }

        internal static IServiceCollection ClientSetup(this IServiceCollection services, AppSettings appSettings)
        {
            services.Configure<CookiePolicyOptions>(options =>
            {
                // This lambda determines whether user consent for non-essential cookies is needed for a given request.
                options.CheckConsentNeeded = context => true;
                options.MinimumSameSitePolicy = SameSiteMode.Strict;
            });
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                options.DefaultSignInScheme = IdentityServerConstants.ExternalCookieAuthenticationScheme;
            })
            .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options => {
                options.Cookie.SameSite = SameSiteMode.None; // must be None for cross-site
                options.Cookie.HttpOnly = true;
                // If secure policy is set as Always then the chrome browser gets stucked and gives a warning.
                // A cookie associated with a cross-site resource at http://google.com/ was set without the SameSite attribute.
                // A future release of Chrome will only deliver cookies with cross-site requests if they are set with SameSite=None and Secure.
                options.Cookie.SecurePolicy = Microsoft.AspNetCore.Http.CookieSecurePolicy.SameAsRequest;
                options.ExpireTimeSpan = TimeSpan.FromHours(12);
                options.SlidingExpiration = true;
                options.EventsType = typeof(CustomCookieAuthenticationEvents);
            })
            .AddOpenIdConnect(IdentityConstant.AzureActiveDirectory, options =>
            {
                options.SignInScheme = IdentityServerConstants.ExternalCookieAuthenticationScheme;
                options.SignOutScheme = IdentityServerConstants.SignoutScheme;

                options.Authority = $"{IdentityConstant.AzureActiveDirectoryAuthority}{appSettings.AzureAD.TenantId}";
                options.ClientId = appSettings.AzureAD.ClientId;
                options.ClientSecret = appSettings.AzureAD.ClientSecret;

                options.ResponseType = OpenIdConnectResponseType.Code;

                // Use configured base URL for Azure AD redirect URIs
                string azureBaseUrl = appSettings.AzureAD.BaseUrl ?? "https://localhost:5001";


                options.CallbackPath = "/signin-aad";
                options.SignedOutCallbackPath = "/signout-callback-aad";
                options.RemoteSignOutPath = "/signout-aad";


                options.Resource = IdentityConstant.AzureActiveDirectoryResource;

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    NameClaimType = "name",
                    RoleClaimType = "role"
                };

                options.SaveTokens = true;

                options.Events = new OpenIdConnectEvents
                {
                    OnTokenValidated = ctx =>
                    {
                        string email = ctx.Principal.FindFirst(ClaimTypes.Email)?.Value ?? string.Empty;
                        string emailFromName = ctx.Principal.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty;
                        Regex regex = new Regex(Constants.Common.EmailFormatRegex);
                        string userName = regex.IsMatch(email) ? email : regex.IsMatch(emailFromName) ? emailFromName : string.Empty;

                        ActiveDirectoryUserDetailsViewModel activeDirectoryUserDetails = new ActiveDirectoryUserDetailsViewModel
                        {
                            AccessToken = ctx.TokenEndpointResponse.AccessToken,
                            UserName = userName
                        };

                        // Setting Access Token in Redis Cache
                        ctx.Response.Cookies.Append("access_token", activeDirectoryUserDetails.AccessToken, Constants.CreateDefaultOptions());

                        Log.Information("Access token Startup - " + activeDirectoryUserDetails.AccessToken);
                        Log.Information("Access token Startup username - " + activeDirectoryUserDetails.UserName);


                        return Task.CompletedTask;
                    },
                    OnRedirectToIdentityProvider = ctx =>
                    {
                        var request = ctx.Request;
                        var uriBuilder = new UriBuilder(appSettings.AzureAD.BaseUrl)
                        {
                            Path = ctx.Options.CallbackPath
                        };

                        ctx.ProtocolMessage.RedirectUri = $"{appSettings.AzureAD.BaseUrl}{ctx.Options.CallbackPath}";
                        ctx.ProtocolMessage.IssuerAddress = ctx.ProtocolMessage.IssuerAddress?.Replace(
                            request.Scheme + "://" + request.Host.Value,
                            appSettings.AzureAD.BaseUrl);
                        return Task.CompletedTask;
                    }
                };
            });

            return services;
        }
    }

}