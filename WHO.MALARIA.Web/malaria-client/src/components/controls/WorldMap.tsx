import { useState, useEffect } from 'react';
import { geoEquirectangular, geoPath } from 'd3-geo';
import { feature } from 'topojson-client';
import { geoData } from './worldMapdata';
import classNames from 'classnames';
import classes from './worldmap.module.scss';
import { CountryDataModel } from '../../models/GlobalDashboardModel';
import {
  WorldMapAssessmentApproachCountries,
  WorldMapAssessmentStatusCountries,
} from '../../models/Enums';

import { lakes } from '../controls/Lakes';
import { disputed_areas } from '../controls/DisputedBorders'


const projection = geoEquirectangular()
  .scale(160)
  .translate([800 / 2, 450 / 2]);

type WorldMapProps = {
  countries: Array<CountryDataModel>;
};
/** Renders world map component */
const WorldMap = (props: WorldMapProps) => {
  const [geographies, setGeographies] = useState([]);
  const { countries } = props;
  const [disputedAreas, setDisputedAreas] = useState<any []>([]);
  

  const disputedStrokeColor = '#ddd';
  const disputedColor = '#c5cdd4';
  const lakeColor = '#5cc7e1'
  const lakeStrokeColor = '#5cc7e1'

  // set world map geographies
  useEffect(
    () => {
      setGeographies(feature(geoData, geoData.objects.countries).features)
      setDisputedAreas(disputed_areas.features)
    },
    []
  );

  // get assessment completion color
  const fill = (d: any): string => {
    switch (
      countries?.find((status: any) => status.iso === d.properties.ISO_A3)
        ?.status
    ) {
      case WorldMapAssessmentStatusCountries.DefaultStatus:
        return '#41bec6';
      case WorldMapAssessmentStatusCountries.InprogessAssessment:
      case WorldMapAssessmentApproachCountries.RapidAssessment:
        return '#948ce1';
      case WorldMapAssessmentStatusCountries.CompletedOneAssessment:
      case WorldMapAssessmentApproachCountries.TailoredAssessment:
        return '#e8bf53';
      case WorldMapAssessmentStatusCountries.MoreThanOneAssessment:
      case WorldMapAssessmentApproachCountries.ComprehensiveAssessment:
        return '#59d37b';
      default:
        return '#bec9d2';
    }
  };

  return (
    <div className={classNames(classes.mapWrapper)}>
      <svg viewBox='-100 -60 1000 500'>
        <g className='countries'>
          {geographies.map((d, i) => (
            <path
              key={`path-${i}`}
              d={geoPath().projection(projection)(d)}
              className='country'
              fill={fill(d)}
              stroke='#ddd'
              strokeWidth={1}
            />
          ))}
        </g>
        {/* ✅ New: Disputed Regions Overlay */}
        <g className='countries'>
                      {disputedAreas.map((d, i) => (
                        <path
                          className='country'
                           key={`disputed-${i}`}
                            d={geoPath().projection(projection)(d)}
                            fill={disputedColor}
                            stroke={disputedStrokeColor}
                            strokeWidth= '0.5'
                            strokeDasharray='0.5'
                        />
                      ))}
        </g>
        
        {/* Lakes */}
        <g>
                      {lakes.features.map((d, i) => (
                        <path
                          key={`lake-${i}`}
                          d={geoPath().projection(projection)(d)}
                          fill={lakeColor}
                          stroke={lakeStrokeColor}
                          strokeWidth= '0.5'
                        />
                      ) )}
        </g>
      </svg>
    </div>
  );
};

export default WorldMap;
