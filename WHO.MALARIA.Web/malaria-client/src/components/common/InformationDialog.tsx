import { Button, Dialog } from "@mui/material";
import React from "react";
import { useTranslation } from "react-i18next";
import { DialogAction } from "../../models/Enums";
import Modal from "../controls/Modal";
import ModalFooter from "../controls/ModalFooter";
import parse from "html-react-parser";
import DOMPurify from 'dompurify';

type InformationDialogProps = {
    open: boolean;
    title: string;
    content: string;
    onClick: (action: DialogAction) => void;
};

/** Renders the information dialog */
function InformationDialog(props: InformationDialogProps) {
    const { t } = useTranslation();
    const { open, title, content, onClick } = props;
    return (
        <Modal
            open={open}
            title={title}
            onEscPress={false}
            onDialogClose={() => onClick(DialogAction.Close)}
        >
            <>
                {parse(DOMPurify.sanitize(content))}
                <ModalFooter>
                    <>
                        <Button
                            className="btn app-btn-primary"
                            onClick={() => onClick(DialogAction.Add)}
                        >
                            {t("Common.Ok")}
                        </Button>
                    </>
                </ModalFooter>
            </>
        </Modal>
    );
}

export default InformationDialog;
