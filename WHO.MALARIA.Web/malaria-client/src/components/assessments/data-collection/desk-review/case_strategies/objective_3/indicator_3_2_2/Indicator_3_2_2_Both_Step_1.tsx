﻿import { useTranslation } from "react-i18next";
import Table from "../../../responses/Table";
import TableCell from "../../../responses/TableCell";
import { TableHead } from "../../../responses/TableHeader";
import TableHeaderCell from "../../../responses/TableHeaderCell";
import parse from "html-react-parser";
import TableRow from "../../../responses/TableRow";
import RadioButtonGroup from "../../../../../../controls/RadioButtonGroup";
import MultiSelectModel from "../../../../../../../models/MultiSelectModel";
import TableFooter from "../../../responses/TableFooter";
import TableBody from "../../../responses/TableBody";
import {
    Step_A_Response,
    TransmitMalariaVariable,
} from "../../../../../../../models/DeskReview/Objective_3/Indicator_3_2_2/Response_3";
import { ChecklistVariableModel } from "../../../../../../../models/DeskReview/ChecklistVariableModel";
import useCalculation from "../../../responses/useCalculation";
import { useSelector } from "react-redux";
import { useLocation } from "react-router-dom";
import useChecklistVariable from "../../../responses/useChecklistVariable";
import { CaseStrategySelectionType, DRVariableCaseCategory, MetNotMetEnum, StrategiesEnum } from "../../../../../../../models/Enums";
import React, { useEffect, useState } from "react";
import { IconButton } from "@mui/material";
import Tooltip from "../../../../../../controls/Tooltip";
import InfoIcon from "@mui/icons-material/Info";
import DOMPurify from 'dompurify';

type Indicator_3_2_2_Props = {
    step_A: Step_A_Response;
    updateStep_A: (step_A: any) => void;
    onValueChange: (field: string, value: any) => void;
};

/** Renders the response for indicator 3.2.2 Step 1 for both strategies(combination of burden reduction and elimination)*/
function Indicator_3_2_2_Both_Step_1(props: Indicator_3_2_2_Props) {
    const { t } = useTranslation(["indicators-responses"]);
    const { step_A, updateStep_A, onValueChange } = props;
    const { hasPVivaxCases, hasMalariaInpatients } = step_A;
    const { calculatePercentageOfYesNo } = useCalculation();
    const errors = useSelector((state: any) => state.error);
    const location: any = useLocation();
    const strategyId: string = location?.state?.strategyId;
    const checkListVariables = useChecklistVariable(strategyId, true);
    const [filteredCheckListVariables, setFilteredCheckListVariables] = useState<Array<ChecklistVariableModel>>([]);

    useEffect(() => {
        onValueChange("checkListVariablesCount", filteredCheckListVariables.length);
    }, [filteredCheckListVariables]);

    useEffect(() => {
        if (checkListVariables.length > 0) {

            let variableList: Array<ChecklistVariableModel> = checkListVariables.filter((variable) => !(variable.caseCategory === DRVariableCaseCategory.PVivax || variable.caseCategory === DRVariableCaseCategory.MalariaInpatient));
            let malariaInpatientsVariables: Array<ChecklistVariableModel> = [];
            let pVivaxCasesVariables: Array<ChecklistVariableModel> = [];

            let transmitMalariaVariableList: Array<TransmitMalariaVariable> = step_A.transmitMalariaVariables.filter((variable) => !(variable.caseCategory === DRVariableCaseCategory.PVivax || variable.caseCategory === DRVariableCaseCategory.MalariaInpatient));
            let transmitMalariaInpatientsVariables: Array<TransmitMalariaVariable> = [];
            let transmitMalariaPVivaxCasesVariables: Array<TransmitMalariaVariable> = [];

            //Checks and show ‘Malaria inpatients variable' if false
            //If true, it doesn't show ‘Malaria inpatients variable' in the checklist variables
            if (strategyId === StrategiesEnum.Both.toLowerCase()) {
                if (step_A.hasMalariaInpatients === false) {
                    //Filters the checklist variables based on case category malaria inpatients variable
                    const malariaInpatientCheckListVariables: Array<ChecklistVariableModel> = checkListVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.MalariaInpatient));

                    malariaInpatientsVariables = [...malariaInpatientCheckListVariables];

                    //Filters and includes transmit malaria variables based on malaria inpatient case category
                    transmitMalariaInpatientsVariables = step_A.transmitMalariaVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.MalariaInpatient));
                } else {
                    malariaInpatientsVariables = [];
                    const malariaInpatientCheckListVariables: Array<ChecklistVariableModel> = checkListVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.MalariaInpatient));

                    //Filters and return variableIds for the malaria inpatients variables
                    const filteredVariableIds: Array<string> = malariaInpatientCheckListVariables.map((variable) => variable.variableId);

                    //Checks and removes ‘Malaria inpatients variable' if true from the variable list
                    const transmitMalariaInpatients = step_A.transmitMalariaVariables.filter((transmitMalariaVariable: TransmitMalariaVariable) => !(filteredVariableIds.includes(transmitMalariaVariable.variableId)) && transmitMalariaVariable.caseCategory === DRVariableCaseCategory.MalariaInpatient);

                    transmitMalariaInpatientsVariables = transmitMalariaInpatients;
                }
            } else {
                //Filters the checklist variables based on case category malaria inpatients variable 
                malariaInpatientsVariables = checkListVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.MalariaInpatient));

                //Filters and includes transmit malaria variables based on malaria inpatient case category
                transmitMalariaInpatientsVariables = step_A.transmitMalariaVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.MalariaInpatient));
            }

            //Checks and show ‘P.Vivax variables’ variable if true
            //If false, it doesn't show ‘P.Vivax variables' in the checklist variables
            if (step_A.hasPVivaxCases === true) {
                //Filters the checklist variables based on case category PVivax variable
                const malariaInpatientCheckListVariables: Array<ChecklistVariableModel> = checkListVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.PVivax));

                pVivaxCasesVariables = [...malariaInpatientCheckListVariables];

                //Filters and includes transmit malaria variables based on PVivax case category
                transmitMalariaPVivaxCasesVariables = step_A.transmitMalariaVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.PVivax));
            } else {
                pVivaxCasesVariables = [];
                const pVivaxCasesCheckListVariables: Array<ChecklistVariableModel> = checkListVariables.filter((variable) => (variable.caseCategory === DRVariableCaseCategory.PVivax));

                //Filters and return variableIds for the PVivax cases variables
                const filteredVariableIds: Array<string> = pVivaxCasesCheckListVariables.map((variable) => variable.variableId);

                //Checks and removes 'P.Vivax variables’ if false from the variable list
                const malariaPVivaxCasesVariables = step_A.transmitMalariaVariables.filter((transmitMalariaVariable: TransmitMalariaVariable) => !(filteredVariableIds.includes(transmitMalariaVariable.variableId)) && transmitMalariaVariable.caseCategory === DRVariableCaseCategory.PVivax);

                transmitMalariaPVivaxCasesVariables = malariaPVivaxCasesVariables;
            }

            //Combined list of all the checklist variables along with malaria inpatients variable and pVivax cases variables
            const checklistMalariaVariables: Array<ChecklistVariableModel> = [...variableList, ...malariaInpatientsVariables, ...pVivaxCasesVariables]

            //Combined list of removed checklist variables if user excludes or not select malaria inpatients variable and pVivax cases variables
            const transmitMalariaChecklistVariables: Array<TransmitMalariaVariable> = [...transmitMalariaVariableList, ...transmitMalariaInpatientsVariables, ...transmitMalariaPVivaxCasesVariables]

            setFilteredCheckListVariables(checklistMalariaVariables);
            updateStep_A({
                ...step_A,
                "transmitMalariaVariables": transmitMalariaChecklistVariables
            });
        }
    }, [checkListVariables, step_A.hasMalariaInpatients, step_A.hasPVivaxCases])

    // Triggered whenever the has pVivax cases are updated
    const onPVivaxCasesChangeHandler = (
        fieldName: string,
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
        updateStep_A({
            ...step_A,
            [fieldName]: e.target.value === "true" ? true : false,
        });
    };

    // Triggered whenever the control values are changed and update response
    const onFieldValueChange = (
        fieldName: string,
        value: any,
        variableId: string
    ) => {
        value = value === "false" ? false : true;
        const transmitMalariaVariables = step_A.transmitMalariaVariables
            ? [...step_A.transmitMalariaVariables]
            : [];
        const variableData: TransmitMalariaVariable | undefined =
            transmitMalariaVariables.find(
                (v: TransmitMalariaVariable) => v.variableId === variableId
            );

        const caseCategoryChecklistVariables: ChecklistVariableModel | undefined = filteredCheckListVariables.find(
            (v: ChecklistVariableModel) => v.variableId === variableId
        );

        if (variableData) {
            variableData[fieldName] = value;
            variableData.caseCategory = caseCategoryChecklistVariables === undefined ? null : caseCategoryChecklistVariables?.caseCategory;
        } else {
            const transmitMalariaVariable = new TransmitMalariaVariable(variableId);
            transmitMalariaVariables.push({
                ...transmitMalariaVariable,
                [fieldName]: value,
                caseCategory: caseCategoryChecklistVariables === undefined ? null : caseCategoryChecklistVariables?.caseCategory
            });
        }

        updateStep_A({ ...step_A, transmitMalariaVariables });
    };

    // Excluded property that are not used in Array Map
    const excludedProperties: Array<string> = [
        "cannotBeAssessed",
        "cannotBeAssessedReason",
    ];

    const topHeaders = [
        t(
            "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:VariableNumber"
        ),
        t(
            "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:RecordedSourceDocuments"
        ),
        t(
            "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Disagregation"
        ),
    ];

    const tableHeaders = [
        {
            field: "",
            value: "",
        },
        {
            field: "",
            value: "",
        },
        {
            field: "under5 ",
            value: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Under5"
            ),
        },
        {
            field: "over5",
            value: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Over5"
            ),
        },
        {
            field: "gender",
            value: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Sex"
            ),
        },
        {
            field: "pregnantWoman",
            value: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:PregnantWoman"
            ),
        },
        {
            field: "healthSector",
            value: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:HealthSector"
            ),
        },
        {
            field: "geography",
            value: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Geography"
            ),
        },
        {
            field: "other",
            value: t(
                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:Other"
            ),
        },
    ];

    //Method creates an array of properties that have checked "Yes"
    const calculateCheckedForProperty = (caseStrategyType: number) => {
        const filteredStrategyTypeCheckListVariables = filteredCheckListVariables.filter((filteredCheckListVariable: ChecklistVariableModel) => filteredCheckListVariable.originallyBelongsTo === caseStrategyType)

        const propertyArray: boolean[] = filteredStrategyTypeCheckListVariables?.map(
            (checkListVariable: ChecklistVariableModel) =>
                getTransmitMalariaVariableValue(
                    checkListVariable.variableId,
                    "recordedInSource"
                )
        );

        return calculatePercentageOfYesNo(propertyArray);
    };

    // get the TransmitMalariaVariable based on the variableId and bind it on change event
    const getTransmitMalariaVariableValue = (
        variableId: string,
        fieldName: string
    ) => {
        const transmitVariable = step_A.transmitMalariaVariables?.find(
            (v: TransmitMalariaVariable) => v.variableId === variableId
        );
        if (!transmitVariable || transmitVariable[fieldName] === undefined)
            return "";

        return transmitVariable[fieldName];
    };

    //Check condition for met and not met and return status
    const getMetNotMetStatus = () => {
        const proportionOfVariablesRecorded = calculateCheckedForProperty(CaseStrategySelectionType.BurdenReduction);

        onValueChange(
            "metNotMetStatus",
            proportionOfVariablesRecorded >= 80
                ? MetNotMetEnum.Met
                : proportionOfVariablesRecorded < 50
                    ? MetNotMetEnum.NotMet
                    : MetNotMetEnum.PartiallyMet
        );
    };

    useEffect(() => {
        getMetNotMetStatus();
    }, [checkListVariables, filteredCheckListVariables, hasMalariaInpatients, hasPVivaxCases]);

    return (
        <>
            <div className="response-wrapper">
                <p>
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:ResponseDesc"
                    )}
                </p>
                <p className="fst-italic">
                    {t(
                        "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:NoteToCompleteAssessment"
                    )}
                </p>

                <div className="row">
                    <div className="col-xs-12 col-md-12 mb-3">
                        <label>
                            {parse(t(
                                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:HasPVivaxCasesQuestion"
                            ))}
                        </label>
                        <RadioButtonGroup
                            id="hasPVivaxCases"
                            name="hasPVivaxCases"
                            row
                            color="primary"
                            options={[
                                new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes")
                                ),
                                new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No")
                                ),
                            ]}
                            value={hasPVivaxCases}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                onPVivaxCasesChangeHandler(
                                    "hasPVivaxCases",
                                    e
                                )
                            }
                            error={errors["step_A.hasPVivaxCases"] && errors["step_A.hasPVivaxCases"]}
                            helperText={errors["step_A.hasPVivaxCases"] && errors["step_A.hasPVivaxCases"]}
                        />
                    </div>

                    <div className="col-xs-12 col-md-12 mb-3">
                        <label>
                            {t(
                                "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:HasMalariaInpatientsQuestion"
                            )}
                            <IconButton className="grid-icon-button">
                                <Tooltip content={t("indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:ToolTipMalariaInpatients")} isHtml>
                                    <InfoIcon fontSize="small" />
                                </Tooltip>
                            </IconButton>
                        </label>
                        <RadioButtonGroup
                            id="hasMalariaInpatients"
                            name="hasMalariaInpatients"
                            row
                            color="primary"
                            options={[
                                new MultiSelectModel(
                                    true,
                                    t("indicators-responses:Common:Yes")
                                ),
                                new MultiSelectModel(
                                    false,
                                    t("indicators-responses:Common:No")
                                ),
                            ]}
                            value={hasMalariaInpatients}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                onPVivaxCasesChangeHandler(
                                    "hasMalariaInpatients",
                                    e
                                )
                            }
                            error={errors["step_A.hasMalariaInpatients"] && errors["step_A.hasMalariaInpatients"]}
                            helperText={errors["step_A.hasMalariaInpatients"] && errors["step_A.hasMalariaInpatients"]}
                        />
                    </div>
                </div>

                <div>
                    {
                        //Show error message if user does not select 'Yes' or 'No' for 'Recorded in source documents' for all variables to finalize the indicator 
                        !!Object.keys(errors).length && (
                            <span className="Mui-error d-flex mb-2">
                                *{" "}
                                {t(
                                    "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:ResponseError"
                                )}
                            </span>
                        )
                    }
                    <Table>
                        <>
                            <TableHead>
                                <th>{parse(topHeaders[0])}</th>
                                <th>{parse(topHeaders[1])}</th>
                                <th>{parse(topHeaders[2])}</th>
                                <th colSpan={6}></th>
                            </TableHead>
                            <TableHead>
                                {tableHeaders.map((head: any) => (
                                    <TableHeaderCell>
                                        <>{parse(DOMPurify.sanitize(head.value))}</>
                                    </TableHeaderCell>
                                ))}
                            </TableHead>
                            <TableBody>
                                <>
                                    {filteredCheckListVariables?.map(
                                        (
                                            checkListVariable: ChecklistVariableModel,
                                            index: number
                                        ) => (
                                            <TableRow
                                                key={`row_${checkListVariable.variableId}_${index}`}
                                            >
                                                <>
                                                    <TableCell width="300px">
                                                        <p className="d-flex mb-0">
                                                            <span className="d-inline-flex me-2">
                                                                {index + 1}{" "}
                                                            </span>{" "}
                                                            {checkListVariable.name}
                                                        </p>
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="recordedInSource"
                                                            name="recordedInSource"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.recorded
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.recorded
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "recordedInSource"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) => {
                                                                onFieldValueChange(
                                                                    "recordedInSource",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                );
                                                                getMetNotMetStatus();
                                                            }}
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].recordedInSource`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].recordedInSource`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="underFive"
                                                            name="underFive"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.underFive
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.underFive
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "underFive"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "underFive",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].underFive`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].underFive`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="overFive"
                                                            name="overFive"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.overFive
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.overFive
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "overFive"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "overFive",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].overFive`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].overFive`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="gender"
                                                            name="gender"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.gender
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.gender
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "gender"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "gender",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].gender`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].gender`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="pregnantWoman"
                                                            name="pregnantWoman"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.pregnantWoman
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.pregnantWoman
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "pregnantWoman"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "pregnantWoman",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].pregnantWoman`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].pregnantWoman`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="healthSector"
                                                            name="healthSector"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.healthSector
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.healthSector
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "healthSector"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "healthSector",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].healthSector`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].healthSector`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="geography"
                                                            name="geography"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.geography
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.geography
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "geography"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "geography",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].geography`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].geography`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                    <TableCell>
                                                        <RadioButtonGroup
                                                            id="other"
                                                            name="other"
                                                            row
                                                            color="primary"
                                                            options={[
                                                                new MultiSelectModel(
                                                                    true,
                                                                    t("indicators-responses:Common:Yes"),
                                                                    !checkListVariable.other
                                                                ),
                                                                new MultiSelectModel(
                                                                    false,
                                                                    t("indicators-responses:Common:No"),
                                                                    !checkListVariable.other
                                                                ),
                                                            ]}
                                                            value={getTransmitMalariaVariableValue(
                                                                checkListVariable.variableId,
                                                                "other"
                                                            )}
                                                            onChange={(
                                                                e: React.ChangeEvent<HTMLInputElement>
                                                            ) =>
                                                                onFieldValueChange(
                                                                    "other",
                                                                    e.currentTarget.value,
                                                                    checkListVariable.variableId
                                                                )
                                                            }
                                                            error={
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].other`
                                                                ] &&
                                                                errors[
                                                                `step_A.transmitMalariaVariables[${index}].other`
                                                                ]
                                                            }
                                                        />
                                                    </TableCell>
                                                </>
                                            </TableRow>
                                        )
                                    )}
                                </>
                            </TableBody>

                            <TableFooter>
                                <TableCell colSpan={2}>
                                    <label>
                                        {t(
                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:WHOEliminationVariablesRecorded"
                                        )}
                                    </label>
                                </TableCell>
                                <TableCell colSpan={8}>
                                    <label>{calculateCheckedForProperty(CaseStrategySelectionType.Elimination)}%</label>
                                </TableCell>
                            </TableFooter>

                            <TableFooter>
                                <TableCell colSpan={2}>
                                    <label>
                                        {t(
                                            "indicators-responses:DRObjective_3_Responses:Indicator_3_2_2:WHOBRVariablesRecorded"
                                        )}
                                    </label>
                                </TableCell>
                                <TableCell colSpan={8}>
                                    <label>{calculateCheckedForProperty(CaseStrategySelectionType.BurdenReduction)}%</label>
                                </TableCell>
                            </TableFooter>
                        </>
                    </Table>
                </div>
            </div>
        </>
    );
}

export default Indicator_3_2_2_Both_Step_1;
