﻿import { GridCellProps, GridColumnProps } from "@progress/kendo-react-grid";
import React from "react";
import { useTranslation } from "react-i18next";
import { TabularReportModel, Column } from "../../../../models/DataAnalysis/DeskReview/IndicatorReportDetailsModel";
import DataGrid from "../../../controls/DataGrid";
import parse from 'html-react-parser';
import { HTMLReactParserOptions, Element, Text } from 'html-react-parser';
import DOMPurify from 'dompurify';

type TabularReportProps = {
    data: TabularReportModel;
    indicatorSequence?: any;
    strategyId?: any;
};

const DATA_ITEM_KEY = "id";
const SELECTED_FIELD = "selected";

/** Data analysis component which renders indicator (table components) to be rendered*/
const TabularReport = (tableData: TabularReportProps) => {
    const { t } = useTranslation();
    const { data } = tableData;

    // get table column
    const rowFields = data.columns;

    // create column definition
    const getColDefs = (): Array<GridColumnProps> => {
        let colDef: Array<GridColumnProps> = [];

        rowFields?.forEach((row: Column, index: number) => {
            colDef.push({
                title: row.label,
                width: `${row.width}px`,
                filterable: false,
                locked: index === 0 ? true : false,
                className: 'cellWrapText',
                cells: {data:(props: GridCellProps) => (
                    <td title={props.dataItem[row.key]}
                        style={props.style} // this applies styles that lock the column at a specific position
                        className={props.className} // this adds classes needed for locked columns
                        colSpan={props.colSpan}
                        role={"gridcell"}
                        aria-colindex={props.ariaColumnIndex}
                        aria-selected={props.isSelected}>
                        {bindIndicatorResponse(tableData.indicatorSequence, props.dataItem[row.key], tableData.strategyId)}
                    </td>
                )},
            })
        });

        return colDef;
    }

    /** Bind indicator response and based on sequence render button to view uploaded image from data collection*/
    const bindIndicatorResponse = (sequence: any, value: string, strategyId: any) => {
        switch (sequence) {
            case '3.3.1':
            case '3.4.1':
                return parse(DOMPurify.sanitize(value) ?? "", {
                    replace: domNode => {
                        if (domNode.type == "tag") {
                            let ele = domNode as Element;
                            let fileContent = ele.attribs.onclick.substring(2, ele.attribs.onclick.length - 2);
                            return <button className="MuiButtonBase-root MuiIconButton-root" onClick={() => showFile(fileContent)}
                                type="button" title="Click to view">
                                <span className="MuiIconButton-label">
                                    <svg className="MuiSvgIcon-root icon-button-primary" focusable="false" viewBox="0 0 24 24" aria-hidden="true">
                                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 
                                                         17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z">
                                        </path>
                                    </svg>
                                </span>
                                <span className="MuiTouchRipple-root"></span>
                            </button>;
                        }
                    }
                })
            default:
                return value;
        }
    }

    const showFile = (fileContent: string) => {
        let url = setScreenshot(fileContent)
        if (url != "") {
            window.open(url)
        }
    }

    const setScreenshot = (content: string) => {
        if (content) {
            const byteCharacters = atob(content);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'image/png' });
            return URL.createObjectURL(blob);
        }

        return "";
    }

    return (
        <>
            <DataGrid
                className={`${data.hasCalculation ? 'k-grid k-grid-wrapper k-grid-action-wrapper k-grid-calculation' : 'k-grid k-grid-wrapper k-grid-action-wrapper k-desk-review'} `}
                columns={getColDefs()}
                data={data.rows}
                dataItemKey={DATA_ITEM_KEY}
                selectedField={SELECTED_FIELD}
                hasActionBtn={true}
                filterable={false}
                selectable={{
                    enabled: true,
                    drag: false,
                    cell: true,
                    mode: "single",
                }}
            />
        </>
    )
}
export default TabularReport;