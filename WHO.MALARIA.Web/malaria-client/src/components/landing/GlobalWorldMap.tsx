import { useState, useEffect } from 'react';
import { geoEquirectangular, geoPath, geoMercator } from 'd3-geo';
import { feature } from 'topojson-client';
import { geoData } from '../controls/worldMapdata';
import { disputed_areas } from '../controls/DisputedBorders'
import classNames from 'classnames';
import { CountryDataModel } from '../../models/GlobalDashboardModel';
import {
  WorldMapAssessmentStatusCountries,
  WorldMapAssessmentApproachCountries,
  MetNotMetStatus,
} from '../../models/Enums';
import { useTranslation } from 'react-i18next';
import { lakes } from '../controls/Lakes';

const projection = geoEquirectangular()
  .scale(160)
  .translate([800 / 2, 450 / 2]);

type WorldMapProps = {
  countries: Array<CountryDataModel> | undefined;
};

/** Renders world map component for global objective dashboard */
const GlobalWorldMap = (props: WorldMapProps) => {
  const [geographies, setGeographies] = useState([]);
  const [disputedAreas, setDisputedAreas] = useState<any []>([]);
  const { countries } = props;
  const { t } = useTranslation();
  const disputedStrokeColor = '#ddd';
  const disputedColor = '#c5cdd4';
  const lakeColor = '#5cc7e1'
  const lakeStrokeColor = '#5cc7e1'

  // Set world map geographies
  useEffect(() => {
    setGeographies(feature(geoData, geoData.objects.countries).features);
    setDisputedAreas(disputed_areas.features)
  }, []);
  
  // get indicator met status completion color
  const fill = (d: any): string => {
    switch (
      countries?.find((status: any) => status.iso === d.properties.ISO_A3)
        ?.status
    ) {
      case MetNotMetStatus.Met:
        return '#6FEDA1'; //green
      case MetNotMetStatus.PartiallyMet:
        return '#F2E56F'; // yellow
      case MetNotMetStatus.NotMet:
        return '#FF9393'; // red
      case MetNotMetStatus.NotAssessed:
        return '#bec9d2'; // blueish gray
      default:
        return '#bec9d2'; // blueish gray
    }
  };

  return (
    <>
      <div className='app-dashboard mt-3'>
        <div className='worldMapWrapper'>
          <svg viewBox='-100 -60 1000 500'>
            <g className='countries'>
              {geographies.map((d, i) => (
                <path
                  key={`path-${i}`}
                  d={geoPath().projection(projection)(d)}
                  className='country'
                  fill={fill(d)}
                  stroke='#ddd'
                  strokeWidth={0.5}
                />
              ))}
            </g>

            {/* ✅ New: Disputed Regions Overlay */}
            <g className='countries'>
              {disputedAreas.map((d, i) => (
                <path
                  className='country'
                   key={`disputed-${i}`}
                    d={geoPath().projection(projection)(d)}
                    fill={disputedColor}
                    stroke={disputedStrokeColor}
                    strokeWidth= '0.5'
                    strokeDasharray='0.5'
                />
              ))}
            </g>

            {/* Lakes */}
            <g>
              {lakes.features.map((d, i) => (
                <path
                  key={`lake-${i}`}
                  d={geoPath().projection(projection)(d)}
                  fill={lakeColor}
                  stroke={lakeStrokeColor}
                  strokeWidth= '0.5'
                />
              ) )}
            </g>
          </svg>
        </div>

        <small className='d-flex mt-2 fst-italic'>
          * {t('Common.GeographicRegionsNote')}
        </small>
      </div>

      <div className='d-flex justify-content-center py-4'>
        <ul className='dashboard-legend'>
          <li>
            <span className='met'></span>
            {t('Common.Met')}
          </li>
          <li>
            <span className='partially-met'></span>
            {t('Common.PartiallyMet')}
          </li>
          <li>
            <span className='not-met'></span>
            {t('Common.NotMet')}
          </li>
          <li>
            <span className='not-assessed'></span>
            {t('Common.NotAssessed')}
          </li>
          <li>
            <span className='disputed'></span>
            {t('Common.disputed')}
          </li>
        </ul>
      </div>
    </>
  );
};

export default GlobalWorldMap;
