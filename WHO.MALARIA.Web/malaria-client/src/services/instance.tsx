﻿import axios, { AxiosInstance, AxiosRequestConfig } from "axios";

import { Constants } from "../models/Constants";
import { StatusCode } from "../models/Enums";
import { ErrorModel } from "../models/ErrorModel";
import { loaderService } from "./notificationService";

export const instance: AxiosInstance = axios.create({
  baseURL: Constants.Api.baseUrl,
});

instance.interceptors.request.use(
  (request: AxiosRequestConfig) => {
    loaderService.setLoading(true);
    return request;
  },
  (error: any) => {
    const { data, status } = error.request;
    loaderService.clearLoading();
    throw new ErrorModel(data?.Message, status);
  }
);

// Add a response interceptor
instance.interceptors.response.use(
  function (response: any) {
    // Any status code that lie within the range of 2xx cause this function to trigger

    if (response.headers["content-type"] === "text/html; charset=utf-8") {
      loaderService.clearLoading();
      throw new ErrorModel(
        `UnAuthorized access. You will be redirected to login page in #TIMER# seconds.`,
        StatusCode.UnAuthorized
      );
    }
    return response;
  },
  function (error: any) {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    const { data, status } = error.response;
    loaderService.clearLoading();
    return Promise.reject(new ErrorModel(data?.Message, status));
  }
);
