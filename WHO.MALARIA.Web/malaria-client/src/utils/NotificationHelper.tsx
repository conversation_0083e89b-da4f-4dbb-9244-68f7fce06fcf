﻿
/** A Notification Helper class which is customized to show Notification */
export default class NotificationHelper {

    /** show the error notification eg. UnAuthorized */
    public show = (message: string) => {
        this.remove();
        const rootClasses = ['msgWrapper', 'MuiSnackbar-root', 'MuiSnackbar-anchorOriginTopCenter'];
        const root = document.createElement('DIV');
        for (var cls of rootClasses) {
            root.classList.add(cls);
        }
       
        root.appendChild(this.createAlertElement(message));
        const rootElement = document.getElementById('root');
        rootElement && rootElement.appendChild(root);
    }

    private remove = () => {
        const childElement = document.body.getElementsByClassName('msgWrapper')[0];
        childElement && childElement.remove();
    }
    private createAlertElement = (message: string) => {
        const rootClasses = ['MuiPaper-root', 'MuiAlert-root', 'MuiAlert-filledError', 'MuiPaper-elevation6'];
        const alertDIV = document.createElement('DIV');

        for (var cls of rootClasses) {
            alertDIV.classList.add(cls);
        }

        alertDIV.appendChild(this.createAlertIconElement());
        alertDIV.appendChild(this.createMessageElement(message));
        alertDIV.appendChild(this.createActionElement());
        return alertDIV;
    }

    private createAlertIconElement = () => {
        const svgClasses = ["MuiSvgIcon-root", "MuiSvgIcon-fontSizeSmall"];
        const alertDIV = document.createElement('DIV')
        const alertIcon = document.createElement('SVG');
        const path = document.createElement('PATH');

        alertDIV.classList.add('MuiAlert-icon');

        alertIcon.setAttribute('focusable', 'false');
        alertIcon.setAttribute('viewBox', '0 0 24 24');
        alertIcon.setAttribute('aria-hidden', 'true');

        for (var className of svgClasses) {
            alertIcon.classList.add(className);
        }

        path.setAttribute('d', 'M11 15h2v2h-2zm0-8h2v6h-2zm.99-5C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8z');


        alertIcon.appendChild(path);
        alertDIV.appendChild(alertIcon);

        return alertDIV;
    }

    private createMessageElement = (message: string) => {
        const msgDIV = document.createElement('DIV');
        msgDIV.classList.add('MuiAlert-message');
        msgDIV.textContent = message;

        return msgDIV;
    }

    private createActionElement = () => {
        const btnClasses = ['MuiButtonBase-root', 'MuiIconButton-root', 'MuiIconButton-colorInherit', 'MuiIconButton-sizeSmall'];
        const actionDIV = document.createElement("DIV");
        const btnAction = document.createElement('BUTTON');
        const rippleSPAN = document.createElement('SPAN');


        for (var cls of btnClasses) {
            btnAction.classList.add(cls);
        }

        actionDIV.classList.add('MuiAlert-action');

        btnAction.click = this.onClose;

        btnAction.appendChild(this.createCloseIconElement());

        rippleSPAN.classList.add('MuiTouchRipple-root');

        btnAction.appendChild(rippleSPAN);
        actionDIV.appendChild(btnAction);

        return actionDIV;
    }

    private createCloseIconElement = () => {
        const svgClasses = ["MuiSvgIcon-root", "MuiSvgIcon-fontSizeSmall"];

        const closeIcon = document.createElement('SVG');
        const path = document.createElement('PATH');
        const iconSPAN = document.createElement('SPAN');



        for (var className of svgClasses) {
            closeIcon.classList.add(className);
        }

        path.setAttribute('d', 'M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z');

        closeIcon.setAttribute('focusable', 'false');
        closeIcon.setAttribute('viewBox', '0 0 24 24');
        closeIcon.setAttribute('aria-hidden', 'true');

        closeIcon.appendChild(path);

        iconSPAN.classList.add('MuiIconButton-label');
        iconSPAN.appendChild(closeIcon);

        return iconSPAN;
    }

    private onClose = () => {
        const element = document.getElementsByClassName('msgWrapper')[0];
        if (element) {
            element.remove();
        }
    }


}


