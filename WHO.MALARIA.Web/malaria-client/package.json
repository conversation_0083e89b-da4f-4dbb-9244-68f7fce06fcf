{"name": "maleria-client", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@babel/code-frame": "^7.14.5", "@mui/icons-material": "^6.1.8", "@progress/kendo-file-saver": "^1.1.1", "@testing-library/jest-dom": "^5.11.10", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^26.0.22", "@types/react-dom": "^19.0.0", "axios": "^1.9.0", "classnames": "^2.3.1", "d3-geo": "^1.11.7", "dompurify": "^3.2.6", "hammerjs": "^2.0.8", "i18next": "^22.5.1", "i18next-browser-languagedetector": "^7.2.1", "i18next-http-backend": "^2.7.0", "js-cookie": "^2.2.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-ga4": "^2.1.0", "react-i18next": "^12.3.1", "sass": "^1.52.3", "terser": "^5.5.1", "topojson-client": "^3.1.0", "typescript": "^4.9.5", "uuid": "^8.3.2", "web-vitals": "^1.1.1"}, "scripts": {"dev": "vite", "build": "NODE_OPTIONS=\"--max-old-space-size=32768\" vite build", "preview": "vite preview", "test": "vitest", "coverage": "vitest run --coverage", "start": "vite", "format": "prettier --write .", "format:check": "prettier --check .", "format:src": "prettier --write src/", "format:check:src": "prettier --check src/", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "lint:src": "eslint src/ --ext .js,.jsx,.ts,.tsx", "lint:fix:src": "eslint src/ --ext .js,.jsx,.ts,.tsx --fix"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@date-io/date-fns": "^1.3.13", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@eslint/js": "^9.27.0", "@mui/lab": "^5.0.0-alpha.176", "@mui/material": "^6.1.8", "@mui/x-date-pickers": "^7.22.2", "@popperjs/core": "^2.9.2", "@progress/kendo-data-query": "^1.5.5", "@progress/kendo-drawing": "^1.17.2", "@progress/kendo-licensing": "^1.6.0", "@progress/kendo-react-animation": "^11.0.0", "@progress/kendo-react-charts": "^11.0.0", "@progress/kendo-react-data-tools": "^11.0.0", "@progress/kendo-react-dateinputs": "^11.0.0", "@progress/kendo-react-dropdowns": "^11.0.0", "@progress/kendo-react-grid": "^11.0.0", "@progress/kendo-react-inputs": "^11.0.0", "@progress/kendo-react-intl": "^11.0.0", "@progress/kendo-react-treeview": "^11.0.0", "@progress/kendo-theme-material": "^11.0.0", "@types/crypto-js": "^4.0.1", "@types/js-cookie": "^2.2.6", "@types/node": "^22.15.21", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.0", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^8.3.3", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "@vitejs/plugin-react": "^4.5.0", "babel-preset-react-app": "^10.0.1", "bootstrap": "^5.0.1", "crypto-js": "^4.0.0", "date-fns": "^2.22.1", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "flag-icon-css": "^3.5.0", "framer-motion": "^12.14.0", "html-react-parser": "^5.2.5", "jsdom": "^26.1.0", "react-redux": "^9.1.2", "react-responsive-carousel": "^3.2.20", "react-router-dom": "^6.28.0", "redux": "^5.0.1", "redux-saga": "^1.3.0", "rxjs": "^7.1.0", "vite": "^6.3.5", "vitest": "^3.1.4"}, "overrides": {"nth-check": ">=2.0.1", "postcss": ">=8.4.31"}, "browser": {"crypto": false}}