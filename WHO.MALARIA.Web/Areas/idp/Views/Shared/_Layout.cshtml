﻿@using Duende.IdentityServer.Extensions
@{ string name = null;
                if (!true.Equals(ViewData["signed-out"]))
                {
                    name = Context.User?.GetDisplayName();
                } }
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] </title>

    <link rel="shortcut icon" href="~/Favicon 2.png">
    <link rel="stylesheet" href="~/css/bootstrap.min.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/font-awesome-min.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mdb.min.css" type="text/css" asp-append-version="true" />
    @*<link rel="stylesheet" href="~/css/rais.site.css" asp-append-version="true" />*@
    <link rel="stylesheet" href="~/css/who.malaria.main.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/fonts/roboto/stylesheet.css" asp-append-version="true" type="text/css" charset="utf-8" style-src />

</head>
<body>
    <partial name="_Header" />
    <div class="container-fluid">

        @RenderBody()


    </div>

    <script src="~/js/jquery.min.js" asp-append-version="true"></script>
    <script src="~/js/bootstrap.min.js" type="text/javascript" asp-append-version="true"></script>
    <script src="~/js/popper.js" type="text/javascript" asp-append-version="true"></script>
    <script src="~/js/mdb.min.js" type="text/javascript" asp-append-version="true"></script>
    <script src="~/js/site.min.js" asp-append-version="true"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js" type="text/javascript" asp-append-version="true"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js" type="text/javascript" asp-append-version="true"></script>
    <script src="~/js/jquery-ui.min.js" type="text/javascript" asp-append-version="true"></script>
    @*<script src="~/js/rais-js/custom-login.js" type="text/javascript" asp-append-version="true"></script>*@

    @RenderSection("Scripts", required: false)
</body>
</html>
