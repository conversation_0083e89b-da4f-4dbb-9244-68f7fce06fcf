﻿@model WHO.MALARIA.Web.Areas.Idp.Models.LogoutViewModel
@{
    ViewData["Title"] = "WHO MALARIA || Logout";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper">
                                    <div>

                                        <div class="loginTitleSection"><h3 class="registerTitle">Logout !!</h3></div>

                                        <div class="loginContentSection">
                                            <div class="loginText-l">
                                                Logout successfully
                                            </div>
                                            <div class="pageLink">
                                                @Html.ActionLink("Back to login", "Login", "Account", new { area = "IDP" }, null)
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
            </div>
        </div>

    </div>
</div>