﻿@model WHO.MALARIA.Web.Areas.Idp.Models.LoginWithRecoveryCodeModel
@{
    ViewData["Title"] = "Recovery Code Verification";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">

                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper authenticator-wrapper">
                                    <div class="loginTitleSection">
                                        <h5 class="registerTitle">@ViewData["Title"]</h5>
                                    </div>
                                    <div class="loginContentSection">
                                        <p>
                                            You have requested to login with a recovery code. This login will not be remembered until you provide
                                            an authenticator app code at login or disable 2FA and login again.
                                        </p>
                                        @if (!string.IsNullOrEmpty(Model.ExhaustRecoveryCodeWarningMessage))
                                        {
                                            <div class="error-message-wrapper">
                                                <span class="error-message">@Model.ExhaustRecoveryCodeWarningMessage</span>
                                            </div>
                                        }
                                        <div class="row">
                                            <div class="col-md-12">
                                                <form method="post">
                                                    <div class="md-form md-outline form-lg input-with-post-icon">
                                                        <i class="fa fa-id-badge input-prefix"></i>
                                                        <input id="RecoveryCode" asp-for="RecoveryCode" class="form-control form-control-lg" autocomplete="off" />
                                                        <label asp-for="RecoveryCode" for="RecoveryCode"></label>
                                                        <span asp-validation-for="RecoveryCode" class="text-danger"></span>
                                                    </div>
                                                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                                                    {
                                                        <div class="error-message-wrapper">
                                                            <span class="error-message">@Model.ErrorMessage</span>
                                                        </div>
                                                    }
                                                    <button type="submit" class="btn btn-mui btn-blue">Log in</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>

    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}
