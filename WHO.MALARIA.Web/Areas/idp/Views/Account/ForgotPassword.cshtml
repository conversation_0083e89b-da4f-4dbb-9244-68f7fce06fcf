﻿
@model WHO.MALARIA.Web.Areas.idp.Models.ForgotPassword
@{
    string inputValueLabelId = Model.InputField.Equals("Username") ? "Username" : "Email";
}
@{
    ViewData["Title"] = "MALARIA || Forgot Password";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper">
                                    <div>
                                        <div class="loginTitleSection">
                                            <h5 class="registerTitle">Forgot Password</h5>
                                            <span>Reset password</span>
                                        </div>
                                        <div class="login-content-section">
                                            @if (Model.IsNotificationSent)
                                            {
                                                <div id="message-success" class="success">
                                                    <span id="btn-close" class="close-btn pull-right">
                                                        <a></a>
                                                    </span>
                                                    <span class="text-success">Password updated successfully</span>
                                                </div>
                                            }

                                            <form id="forgotPasswordForm" method="post" class="form-horizontal">
                                                @Html.AntiForgeryToken()
                                                <fieldset>
                                                    <div class="form-group row">

                                                        <div class="col-sm-6 text-center">
                                                            <label class="control-label" for="Username">
                                                                <input type="radio" id="Username" name="InputField" value="Username" onchange="FieldOnChange('Username')" checked="@Model.InputField.Equals("Username")" />

                                                                <span>UserName</span>
                                                            </label>
                                                        </div>
                                                        <div class="col-sm-6 text-center">
                                                            <label class="control-label" for="Email">
                                                                <input type="radio" id="Email" name="InputField" value="Email" onchange="FieldOnChange('Email')" checked="@Model.InputField.Equals("Email")" />
                                                                <span>Email</span>
                                                            </label>
                                                        </div>
                                                    </div>

                                                    <div class="md-form md-outline form-lg input-with-post-icon">
                                                        <div>
                                                            <i id="InputValueIcon" class="fa fa-address-book input-prefix"></i>
                                                            <input type="text" class="form-control validate-equalTo-blur form-control-lg"
                                                                   autocomplete="off" data-val="true"
                                                                   id="InputValue" name="InputValue"
                                                                   aria-describedby="InputValue-error" aria-invalid="true" maxlength="255">
                                                            <label id="lblInputValue" for="InputValue">@inputValueLabelId</label>
                                                            <span class="error-message username-null-error-message hidden">Username is required</span>
                                                            <span class="error-message email-null-error-message hidden">Email is required</span>
                                                        </div>
                                                        <div class="error-message-wrapper">
                                                            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                                                            {
                                                                <span class="error-message">@Model.ErrorMessage</span>
                                                            }
                                                        </div>
                                                    </div>


                                                    <div class="form-group row">
                                                        <div class="col-sm-12">
                                                            <button class="btn btn-mui btn-blue" id="btnSubmit" type="button" name="button" value="submit">Submit</button>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row">
                                                        <div class="col-sm-12">
                                                            <a href="@Url.Action("Login")">
                                                                Back to login
                                                            </a>
                                                        </div>
                                                    </div>
                                                </fieldset>
                                            </form>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</div>

<script src="~/js/jquery.min.js" asp-append-version="true" type="text/javascript"></script>