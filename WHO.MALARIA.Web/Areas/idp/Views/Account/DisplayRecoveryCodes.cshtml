﻿@model WHO.MALARIA.Web.Areas.Idp.Models.DisplayRecoveryCodeModel

@{
    ViewData["Title"] = "Recovery Codes";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper authenticator-wrapper">
                                    <div class="loginTitleSection">
                                        <h5 class="registerTitle">@ViewData["Title"]</h5>
                                    </div>
                                    <div class="loginContentSection">
                                        @if (Model.RecoveryCodes != null)
                                        {
                                            <div class="alert alert-warning" role="alert">
                                                <p>
                                                    <span class="glyphicon glyphicon-warning-sign"></span>
                                                    <strong>Put these codes in a safe place. These codes are only displayed once on this page and will not be shown again.</strong>
                                                </p>
                                                <p>
                                                    If you lose your device and don't have the recovery codes you will lose access to your account.
                                                </p>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="recovery-codes">
                                                        @for (var row = 0; row < Model.RecoveryCodes.Length; row += 2)
                                                        {
                                                            <code>@Model.RecoveryCodes[row]</code>

                                                            <code>@Model.RecoveryCodes[row + 1]</code>

                                                        }
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            if (Model.RecoveryCodesAvailable > 0)
                                            {
                                                <div class="alert alert-warning" role="alert">
                                                    <p>
                                                        You have @Model.RecoveryCodesAvailable recovery codes available.
                                                    </p>
                                                </div>
                                            }
                                            else
                                            {
                                                <div class="alert alert-warning" role="alert">
                                                    <p>
                                                        You have no recovery codes left. You must generate new set of recovery codes before you log in with a recovery code.
                                                    </p>
                                                </div>
                                            }
                                        }

                                        <div class="btn btn-mui btn-blue">

                                            @if (Model.IsInitial2FASetup)
                                            {
                                                @Html.ActionLink("Back to login", "Login", "account", new { area = "idp" }, null)
                                            }
                                            else
                                            {
                                                @Html.ActionLink("Go to dashboard", "Dashboard", "Home", new { area = string.Empty }, null)
                                            }
                                        </div>

                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}
