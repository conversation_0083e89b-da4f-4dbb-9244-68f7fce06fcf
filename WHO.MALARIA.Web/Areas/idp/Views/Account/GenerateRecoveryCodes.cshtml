﻿@{
    ViewData["Title"] = "Generate two-factor authentication (2FA) recovery codes";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper authenticator-wrapper">
                                    <div class="loginContentSection">
                                        <div class="loginTitleSection">
                                            <h5 class="registerTitle">@ViewData["Title"]</h5>
                                        </div>

                                        <div class="alert alert-warning" role="alert">
                                            <p>
                                                <span class="glyphicon glyphicon-warning-sign"></span>
                                                <strong>This action generates new recovery codes.</strong>
                                            </p>
                                            <p>
                                                If you lose your device and don't have the recovery codes you will lose access to your account.
                                            </p>
                                            <p>
                                                Generating new recovery codes does not change the keys used in authenticator apps.
                                            </p>
                                            <p>
                                                Old recovery codes will not be valid after generating the new set of recovery codes.
                                            </p>
                                        </div>

                                        <div>
                                            <form asp-action="UpdateRecoveryCodes" method="post" class="form-group">
                                                <button class="btn btn-mui btn-blue" type="submit">Generate Recovery Codes</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</div>