﻿@model WHO.MALARIA.Web.Areas.Idp.Models.UpdatePassword
@{
    ViewData["Title"] = "RAIS || Password Link Expired";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper">
                                    <div class="loginTitleSection">
                                        <h3 class="registerTitle">
                                            Update Password Link Expired
                                        </h3>
                                    </div>
                                    <div class="loginContentSection">
                                        <div class="loginText-l">
                                            Update Password Link Expired Message
                                        </div>
                                        <div class="pageLink">
                                            <a href="@Url.Action("Login")">Back to login</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>

    </div>
</div>