﻿@{
    ViewData["Title"] = "Re-configure Authenticator App";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper authenticator-wrapper">
                                    <div class="loginTitleSection">
                                        <h5 class="registerTitle">@ViewData["Title"]</h5>
                                    </div>

                                    <div class="loginContentSection">
                                        <div class="alert alert-warning" role="alert">
                                            <p>
                                                This process disables 2FA until you re-configure your authenticator app.
                                                If you do not configure  your authenticator app , you may lose access to your account.
                                            </p>
                                        </div>
                                        <div>
                                            <form asp-action="UpdateAuthenticator" method="post" class="form-group">
                                                <button class="btn btn-mui btn-blue" type="submit">Reconfigure Authenticator App</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>

    </div>
</div>