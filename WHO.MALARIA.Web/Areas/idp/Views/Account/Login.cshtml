@using IdentityModel;
@using Duende.IdentityServer.Events;
@using Duende.IdentityServer.Extensions;
@using Duende.IdentityServer.Models;
@using Duende.IdentityServer.Services;
@using Duende.IdentityServer.Stores;
@model WHO.MALARIA.Web.Areas.Idp.Models.LoginViewModel
@{ string labelId = string.Empty; }
@{ ViewData["Title"] = "WHO MALARIA || Login"; }

<!--<div class="container-fluid">
    <div class="row no-gutter">
        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">-->
@* Login Background Image Section*@
<!--<partial name="_LoginBGPartial.cshtml" />
</div>

<div class="col-md-6 col-lg-4 bg-login">
    <div class="login d-flex align-items-center py-5">
        <div class="container">-->
@* Login Logo Section*@
<!--<partial name="_LoginLogoPartial.cshtml" />-->
@*Internal WHO AD Login*@
<!--<div class="row who-panel">
    <!--<div class="col-md-12 col-lg-12 mx-auto" style="display:none">-->
-->
<!--@* InternalAuthentication login partial view  *@-->
<!--<partial name="_InternalAuthentication" model="Model" />
</div>-->
<!--<div class="col-md-12 col-lg-12 mx-auto">
                            <div id="main-screen">
                                <partial name="_MainLogin" model="Model" />
                                <div class="col-md-12 col-12 mt-5 text-md-center">
                                    <a data-selected-user="" class="btn btn-primary proceed">LETS GET STARTED</a>
                                </div>
                            </div>
                            <div id="external-screen" style="display:none">
                                <partial name="_ExternalAuthentication" model="Model" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>-->
<!-- Button trigger modal -->
<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#staticBackdrop">
    Launch static backdrop modal
</button>

<!-- Modal -->
<div class="modal fade" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="staticBackdropLabel">Modal title</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                ...
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Understood</button>
            </div>
        </div>
    </div>
</div>
<script src="~/js/jquery.min.js" asp-append-version="true" type="text/javascript"></script>

@section Scripts{
    <script src="~/js/auth/who.malaria.auth.js" type="text/javascript" asp-append-version="true"></script>
}