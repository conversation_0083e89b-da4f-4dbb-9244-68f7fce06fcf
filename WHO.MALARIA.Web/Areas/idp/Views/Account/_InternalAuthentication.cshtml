﻿
@model WHO.MALARIA.Web.Areas.Idp.Models.LoginViewModel

<div class="login-page">
    <div class="login-wrapper">

        <!-- Nav tabs -->
        <ul class="nav nav-tabs" role="tablist">
            @if (Model.LocalAuthenticationEnabled)
            {
                <li role="presentation">
                    <a href="#local-login" class="@(Model.LocalAuthenticationEnabled ? "active" : "")" aria-controls="home" role="tab" data-toggle="tab">@Model.LocalAuthenticationLabel </a>
                </li>
}
            @if (Model.WindowsAuthenticationEnabled)
            {
                <li role="presentation">
                    <a href="#external-login" class="@(Model.WindowsAuthenticationEnabled && !Model.LocalAuthenticationEnabled ? "active" : "")" aria-controls="profile" role="tab" data-toggle="tab">@Model.WindowsAuthenticationLabel</a>
                </li>
}
        </ul>

        <!-- Tab panes -->
        <div class="tab-content">

            <!-- Password reset success message -->
            @if (Model.IsPasswordReset)
            {
                <div id="message-success" class="success">
                    <span id="btn-close" class="close-btn pull-right">
                        <a></a>
                    </span>
                    <span class="text-success">Password updated successfully</span>
                </div>}

            @if (Model.LocalAuthenticationEnabled)
            {
                <div role="tabpanel" class="@(Model.LocalAuthenticationEnabled ? "tab-pane active" : "tab-pane")" id="local-login">
                    <form asp-route="Login" class="form-horizontal">
                        <input type="hidden" asp-for="ReturnUrl" />
                        <fieldset>
                            <div class="md-form md-outline form-lg input-with-post-icon">
                                <i class="fa fa-address-book input-prefix"></i>
                                <input class="form-control valid form-control-lg" type="text" data-val="true" id="Username" name="Username" value="" aria-describedby="Username-error"
                                       aria-invalid="false" autocomplete="off" data-val-required="Username is required">
                                <label for="Username">
                                    Username
                                </label>
                                <span asp-validation-for="Username" class="error-message"></span>
                            </div>

                            <div class="md-form md-outline form-lg input-with-post-icon">
                                <i class="fa fa-lock input-prefix"></i>
                                <input class="form-control valid form-control-lg" type="password" data-val="true" id="Password" name="Password" value="" aria-describedby="Password-error"
                                       aria-invalid="false" autocomplete="off" data-val-required="Password is required" oncopy="return false" onpaste="return false">
                                <label for="Password">
                                    Password
                                </label>
                                <span asp-validation-for="Password" class="error-message"></span>
                            </div>

                            <div class="error-message-wrapper">
                                @if (!Model.IsValidUser && !string.IsNullOrEmpty(Model.Username))
                                {
                                    <span id="InvalidCredential-Error" class="error-message">@Model.LoginFailedErrorMessage</span>}

                            </div>

                            @if (Model.AllowRememberLogin)
                            {
                                <div class="form-group login-remember">
                                    <label asp-for="RememberLogin">
                                        <input asp-for="RememberLogin">
                                        <strong>Remember Me </strong>
                                    </label>
                                </div>}
                            <div class="form-group row">

                                <div class="col-sm-12">
                                    <button id="btnLogIn" class="btn btn-mui btn-blue" name="button" value="login">Login</button>

                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-12">
                                    <a href="@Url.Action("ForgotPassword")">
                                        Forgot Password
                                    </a>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-sm-12">
                                    <a class="btn btn-primary" href="@Url.Action("GoogleLogin","Account")">Log In With Google</a>
                                    @*<a class="btn btn-primary" asp-area="idp" asp-controller="Account" asp-action="GoogleLogin">Log In With Google</a>*@
                                </div>
                            </div>
                        </fieldset>
                    </form>
                </div>}

            @if (Model.WindowsAuthenticationEnabled && Model.VisibleExternalProviders.Any())
            {
                <div role="tabpanel" class="@(Model.WindowsAuthenticationEnabled && !Model.LocalAuthenticationEnabled ? "tab-pane active" : "tab-pane")" id="external-login">
                    <ul class="list-inline">
                        @foreach (var provider in Model.VisibleExternalProviders)
                        {
                            <li>
                                <a class="btn btn-mui btn-blue"
                                   asp-controller="External"
                                   asp-action="Challenge"
                                   asp-route-provider="@provider.AuthenticationScheme"
                                   asp-route-returnUrl="@Model.ReturnUrl">
                                    @provider.DisplayName
                                </a>
                            </li>
}
                    </ul>
                </div>}

            @if (!Model.EnableLocalLogin && !Model.VisibleExternalProviders.Any())
            {
                <div class="alert alert-warning">
                    <strong>Invalid login request</strong>
                    There are no login schemes configured for this client.
                </div>}

        </div>

    </div>
</div>