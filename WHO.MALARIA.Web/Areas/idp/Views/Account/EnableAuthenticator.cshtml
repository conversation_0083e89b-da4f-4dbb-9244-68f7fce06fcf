﻿@model WHO.MALARIA.Web.Areas.Idp.Models.EnableAuthenticatorModel
@{
    ViewData["Title"] = "RAIS || Configure Authenticator App";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page enable-authenticator">
                                <div class="login-wrapper authenticator-wrapper">

                                    <div class="loginContentSection">

                                        <div class="form-group row">
                                            <div class="col-md-12">
                                                <form method="post">
                                                    <div class="md-form md-outline form-lg input-with-post-icon">
                                                        <i class="fa fa-qrcode input-prefix"></i>
                                                        <input id="VerificationCode" asp-for="Code" class="form-control form-control-lg" autocomplete="off" />
                                                        <label for="VerificationCode">Verification Code</label>
                                                        <span asp-validation-for="Code" class="text-danger"></span>
                                                    </div>

                                                    <button type="submit" class="btn btn-mui btn-blue">Verify</button>
                                                    <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                                                </form>
                                            </div>
                                        </div>

                                        <p><b>To use an authenticator app go through the following steps:</b></p>
                                        <ol class="list">
                                            <li>
                                                <p>
                                                    Download a two-factor authenticator app like Microsoft Authenticator for
                                                    <a href="https://go.microsoft.com/fwlink/?Linkid=825071">Windows Phone</a>,
                                                    <a href="https://go.microsoft.com/fwlink/?Linkid=825072">Android</a> and
                                                    <a href="https://go.microsoft.com/fwlink/?Linkid=825073">iOS</a> or
                                                    Google Authenticator for
                                                    <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&amp;hl=en">Android</a> and
                                                    <a href="https://itunes.apple.com/us/app/google-authenticator/id388497605?mt=8">iOS</a>.
                                                </p>
                                            </li>
                                            <li>
                                                <p>Scan the QR Code or enter this key <kbd class="QR-code">@Model.SharedKey</kbd> into your two factor authenticator app. Spaces and casing do not matter.</p>
                                                <div id="qrCode"></div>
                                                <div id="qrCodeData" data-url="@Html.Raw(Model.AuthenticatorUri)"></div>
                                            </li>
                                            <li>
                                                <p>
                                                    Once you have scanned the QR code or input the key above, your two factor authentication app will provide you
                                                    with a unique code. Enter the code in the confirmation box below.
                                                </p>
                                            </li>
                                        </ol>
                                    </div>

                                </div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="authenticatorUri" value="@Model.AuthenticatorUri" />
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")


    @*Script Reference for QR Code generator JS*@
    <script src="~/js/qrCodes/qrcode.js" asp-append-version="true"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
}
