﻿@model WHO.MALARIA.Web.Areas.Idp.Models.UpdatePassword
@{
    ViewData["Title"] = "WHO MALARIA || Update Password";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">
                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper">

                                    <div class="loginTitleSection">
                                        <h4 class="registerTitle">Create Your Password</h4>
                                        <span>Create Your Password Help</span>
                                    </div>

                                    <div class="login-content-section">
                                        @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                                        {
                                            <div id="message-success" class="success">
                                                <span id="btn-close" class="close-btn pull-right">
                                                    <a></a>
                                                </span>
                                                <span class="text-success">Password link has been sent to your email id</span>
                                            </div>
                                        }

                                        <form method="post" class="form-horizontal">
                                            @Html.AntiForgeryToken()
                                            <input type="hidden" asp-for="UpdatePasswordLink" />
                                            <fieldset>
                                                <div class="md-form md-outline form-lg input-with-post-icon">
                                                    <i class="fa fa-lock input-prefix"></i>
                                                    <input type="password" class="form-control validate-equalTo-blur password form-control-lg"
                                                           autocomplete="off" data-val="true"
                                                           data-val-regex="Strong password required"
                                                           data-val-regex-pattern="^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).{8,30}$"
                                                           data-val-required="Password is required" id="Password" name="Password" maxlength="50"
                                                           aria-describedby="Password-error" aria-invalid="true" oncopy="return false" onpaste="return false">
                                                    <label for="c">
                                                        Password
                                                    </label>
                                                    <span asp-validation-for="Password" class="error-message"></span>
                                                </div>

                                                <div class="md-form md-outline form-lg input-with-post-icon">
                                                    <i class="fa fa-lock input-prefix"></i>
                                                    <input type="password" class="form-control password form-control-lg" autocomplete="off" data-val="true"
                                                           data-val-equalto="Password and confirm password not match"
                                                           data-val-equalto-other="*.Password"
                                                           data-val-required="Confirm Password is reuqired" id="ConfirmPassword" name="ConfirmPassword" maxlength="50"
                                                           aria-describedby="ConfirmPassword-error" aria-invalid="true" oncopy="return false" onpaste="return false">
                                                    <label for="ConfirmPassword">
                                                        Confirm Password
                                                    </label>
                                                    <span asp-validation-for="ConfirmPassword" class="error-message"></span>
                                                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                                                    {
                                                        <span id="spErrorMessage" class="error-message">@Model.ErrorMessage</span>
                                                    }
                                                </div>


                                                <div class="form-group row">
                                                    <div class="col-sm-12">
                                                        <button class="btn btn-mui btn-blue" type="submit" name="button" value="submit">Submit</button>
                                                    </div>
                                                </div>

                                                <div class="form-group row">
                                                    <div class="col-sm-12">
                                                        <a href="@Url.Action("Login")">Back to login</a>
                                                    </div>
                                                </div>

                                            </fieldset>
                                        </form>

                                    </div>

                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>


    </div>
</div>

<script src="~/lib/jquery/dist/jquery.min.js" asp-append-version="true" type="text/javascript"></script>