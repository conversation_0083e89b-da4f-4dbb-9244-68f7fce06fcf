﻿@model WHO.MALARIA.Web.Areas.Idp.Models.Login2faViewModel
@{
    ViewData["Title"] = "Two-factor Authentication";
}

<div class="container-fluid">
    <div class="row no-gutter">

        <div class="d-none d-md-flex col-md-6 col-lg-8 bg-login-image">
            @* Login Background Image Section*@
            <partial name="_LoginBGPartial.cshtml" />
        </div>

        <div class="col-md-6 col-lg-4 bg-login">
            <div class="login d-flex align-items-center py-5">
                <div class="container">

                    @* Login Logo Section*@
                    <partial name="_LoginLogoPartial.cshtml" />

                    <div class="row">
                        <div class="col-md-12 col-lg-12 mx-auto">

                            @* Login Content Section *@
                            <div class="login-page">
                                <div class="login-wrapper authenticator-wrapper">
                                    <div class="loginTitleSection">
                                        <h5 class="registerTitle">@ViewData["Title"]</h5>
                                    </div>

                                    <div class="loginContentSection">
                                        <p>Your login is protected with the Google authenticator app. Enter your authenticator code below as displayed in the Google Authenticator app.</p>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <form method="post">
                                                    <div class="md-form md-outline form-lg input-with-post-icon">
                                                        <i class="fa fa-qrcode input-prefix"></i>
                                                        <input id="TwoFactorCode" asp-for="TwoFactorCode" class="form-control form-control-lg" autocomplete="off" />
                                                        <label asp-for="TwoFactorCode" for="TwoFactorCode"></label>
                                                        <span asp-validation-for="TwoFactorCode" class="text-danger"></span>
                                                    </div>

                                                    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                                                    {
                                                        <div class="error-message-wrapper">
                                                            <span class="error-message">@Model.ErrorMessage</span>
                                                        </div>
                                                    }
                                                    <div class="form-group">
                                                        <button type="submit" class="btn btn-mui btn-blue">Log in</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                        <p>
                                            Don't have access to your authenticator device? You can
                                            <a asp-area="idp" asp-controller="Account" asp-action="LoginWithRecoveryCode">log in with a recovery code</a>.
                                        </p>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
            </div>
        </div>

    </div>
</div>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
}
