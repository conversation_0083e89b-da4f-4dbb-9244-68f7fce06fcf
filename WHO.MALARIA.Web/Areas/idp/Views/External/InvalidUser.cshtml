﻿@model WHO.MALARIA.Web.Areas.idp.Models.InvalidUserModel
@{
    ViewData["Title"] = "Invalid User";
}
<div class="login-page">
    <h2 class="form-signin-heading">RAIS+</h2>

    <div class="login-wrapper">
        <div>

            <div class="loginTitleSection"><h3 class="registerTitle">Access Denied !!</h3></div>

            <div class="loginContentSection">
                <div class="loginText-l">
                    @Model.Message
                </div>
                <div class="pageLink">
                    @Html.ActionLink("Back to login", "Login", "Account", new { area = "idp" }, null)
                </div>
            </div>

        </div>
    </div>
</div>
