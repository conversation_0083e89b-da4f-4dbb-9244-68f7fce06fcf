﻿// Copyright (c) <PERSON> & <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.


using System;

namespace WHO.MALARIA.Web.Areas.Idp.Models
{
    public class AccountOptions
    {
        public static bool AllowLocalLogin = true;
        public static bool AllowRememberLogin = false;
        public static TimeSpan RememberMeLoginDuration = TimeSpan.FromDays(30);

        public static bool ShowLogoutPrompt = false;
        public static bool AutomaticRedirectAfterSignOut = true;

        // specify the Windows authentication scheme being used
        public static readonly string WindowsAuthenticationSchemeName = Microsoft.AspNetCore.Server.IISIntegration.IISDefaults.AuthenticationScheme;
        //public static readonly string WindowsAuthenticationSchemeName = "OpenIdConnect";

        // if user uses windows auth, should we load the groups from windows
        public static bool IncludeWindowsGroups = true;

        public static string InvalidCredentialsErrorMessage = "Invalid username or password";
    }
}
