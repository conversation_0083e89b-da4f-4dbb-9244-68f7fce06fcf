// Copyright (c) <PERSON> & <PERSON>. All rights reserved.
// Licensed under the Apache License, Version 2.0. See LICENSE in the project root for license information.

using System;
using System.Collections.Generic;
using System.Linq;

namespace WHO.MALARIA.Web.Areas.Idp.Models
{
    public class LoginViewModel : LoginInputModel
    {
        public bool AllowRememberLogin { get; set; } = true;

        public bool EnableLocalLogin { get; set; } = true;

        public IEnumerable<ExternalProvider> ExternalProviders { get; set; } = Enumerable.Empty<ExternalProvider>();
        //public IEnumerable<ExternalProvider> VisibleExternalProviders => ExternalProviders.Where(x => !String.IsNullOrWhiteSpace(x.DisplayName));
        public IEnumerable<ExternalProvider> VisibleExternalProviders => ExternalProviders;

        public bool IsExternalLoginOnly => EnableLocalLogin == false && ExternalProviders?.Count() == 1;

        public string ExternalLoginScheme => IsExternalLoginOnly ? ExternalProviders?.SingleOrDefault()?.AuthenticationScheme : null;

        public bool IsValidUser { get; set; }

        // Log-in attempt failed error message. 
        public string LoginFailedErrorMessage { get; set; }

        // Boolean status of reset password success
        // Used to show reset password success message on login page as we redirect to login page from reset password page after password reset successfully 
        public bool IsPasswordReset { get; set; }

        public string WindowsAuthenticationLabel { get; set; }
        public string LocalAuthenticationLabel { get; set; }
        public bool WindowsAuthenticationEnabled { get; set; }
        public bool LocalAuthenticationEnabled { get; set; }

    }
}