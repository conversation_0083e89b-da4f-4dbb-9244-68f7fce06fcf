﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace WHO.MALARIA.Web.Areas.Idp.Models
{
    public class DisplayRecoveryCodeModel
    {
        //List of available recovery codes
        public string[] RecoveryCodes { get; set; }

        //Indicates the number of recovery codes available to be redeemed
        public int RecoveryCodesAvailable { get; set; }
        
        //Indicates if the view is rendering after initial 2FA setup or after regenerating the recovery codes
        public bool IsInitial2FASetup { get; set; }
    }
}
