﻿using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Web.Areas.Idp.Helper
{
    /// <summary>
    /// The class is responsible for the Role Management.
    /// As no Role related CRUD operation are required as of now, the methods have not implemented.
    /// </summary>
    public class RoleStore : IRoleStore<UserRoleDto>
    {
        #region Declaration


        #endregion

        #region Public Constructor

        public RoleStore()
        {
        }

        #endregion

        #region Empty Implementations

        public Task<UserRoleDto> FindByIdAsync(string roleId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<UserRoleDto> FindByNameAsync(string internalRoleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<IdentityResult> CreateAsync(UserRoleDto role, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();
            throw new NotImplementedException();
        }

        public Task<IdentityResult> UpdateAsync(UserRoleDto role, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();
            throw new NotImplementedException();
        }

        public Task<IdentityResult> DeleteAsync(UserRoleDto role, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();
            throw new NotImplementedException();
        }

        public Task<string> GetRoleIdAsync(UserRoleDto role, CancellationToken cancellationToken)
        {
            return Task.FromResult(role.Id.ToString());
        }

        public Task<string> GetRoleNameAsync(UserRoleDto role, CancellationToken cancellationToken)
        {
            return Task.FromResult(role.Name);
        }

        public Task SetRoleNameAsync(UserRoleDto role, string roleName, CancellationToken cancellationToken)
        {
            role.Name = roleName;
            return Task.FromResult(0);
        }

        public Task<string> GetNormalizedRoleNameAsync(UserRoleDto role, CancellationToken cancellationToken)
        {
            return Task.FromResult(role.Name);
        }

        public Task SetNormalizedRoleNameAsync(UserRoleDto role, string normalizedName, CancellationToken cancellationToken)
        {
            role.Name = normalizedName;
            return Task.FromResult(0);
        }


        #endregion

        #region Dispose Implementation 

        public void Dispose()
        {
            // Nothing to dispose.
        }

        #endregion 
    }
}
