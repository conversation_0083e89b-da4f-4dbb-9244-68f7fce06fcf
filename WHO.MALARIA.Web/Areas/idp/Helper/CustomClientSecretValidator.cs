﻿//used for avoiding ambiguous name for constants in web project and model project
using IdentityConstant = WHO.MALARIA.Domain.Constants.Constants.IdentityConstant;
using Duende.IdentityServer.Models;
using Duende.IdentityServer.Services;
using Duende.IdentityServer.Stores;
using Duende.IdentityServer.Validation;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using System.Linq;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Models.Identity;

namespace WHO.MALARIA.Web.Areas.Idp.Helper
{
    /// <summary>
    /// Validate the STS clients such as API clients which are stored in database. Web client is not supposed to be validated against the client secret in the current implementation.
    /// </summary>
    public class CustomClientSecretValidator : IClientSecretValidator
    {

        #region Declaration

        private readonly ILogger _logger;
        private readonly IClientStore _clients;
        private readonly IEventService _events;
        private readonly SecretValidator _validator;
        private readonly SecretParser _secretParser;
        private readonly IPasswordHasher<Identity> _passwordHasher;

        #endregion

        #region Public Constructor

        /// <summary>
        /// Initializes a new instance of the <see cref="ClientSecretValidator"/> class.
        /// </summary>
        /// <param name="clients">The clients.</param>
        /// <param name="secretParser">The parser.</param>
        /// <param name="validator">The validator</param>
        /// <param name="events">The events.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="passwordHasher">Password Hasher</param>
        public CustomClientSecretValidator(IClientStore clients, SecretParser secretParser, SecretValidator validator, IEventService events, ILogger<ClientSecretValidator> logger, IPasswordHasher<Identity> passwordHasher)
        {
            _clients = clients;
            _secretParser = secretParser;
            _validator = validator;
            _events = events;
            _logger = logger;
            _passwordHasher = passwordHasher;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Validate the client secret key against the hashed value saved in Identity table against the client id
        /// </summary>
        /// <param name="context"></param>
        /// <returns></returns>
        public async Task<ClientSecretValidationResult> ValidateAsync(HttpContext context)
        {
            ClientSecretValidationResult validationResult = new ClientSecretValidationResult
            {
                IsError = true
            };

            // Get the parsed secret from the context
            ParsedSecret parsedSecret = await _secretParser.ParseAsync(context);
            if (parsedSecret == null)
            {
                return validationResult;
            }

            // load client
            var client = await _clients.FindEnabledClientByIdAsync(parsedSecret.Id);

            if (client == null)
            {
                // LoggingHelper.LogError($"No client with id '{parsedSecret.Id}' found. aborting");

                return validationResult;
            }

            if (client.RequireClientSecret && !client.IsImplicitOnly())
            {
                // Check if the client secret is valid or not and web service account is active or inactive
                if (_passwordHasher.VerifyHashedPassword(null, client.ClientSecrets.Single().Value.ToString(),
                    parsedSecret.Credential.ToString()) == PasswordVerificationResult.Success &&
                    client.Claims.Any(c => c.Type == IdentityConstant.IsUserActive && c.Value == IdentityConstant.True))
                {

                    validationResult.IsError = false;
                    validationResult.Client = client;
                    validationResult.Secret = parsedSecret;

                    return validationResult;
                }
                // LoggingHelper.LogError($"Client secret validation failed for client: { client.ClientId}.");

                return validationResult;
            }

            // when no client secret validation required , return success.
            validationResult.IsError = false;
            validationResult.Client = client;

            return validationResult;
        }

        #endregion

    }
}
