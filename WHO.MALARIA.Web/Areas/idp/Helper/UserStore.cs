﻿
using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;

namespace WHO.MALARIA.Web.Areas.Idp.Helper
{
    /// <summary>
    /// In the current implementation of AspNetCore Identity , EntityFrameworkCore implementation has been replaced with the ADO.Net
    /// implementation.
    /// User Store is responsible for performing the activities related to Authentication and setting the properties of Identity Model in RAIS
    /// </summary>
    public class UserStore : IUserRoleStore<IdentityDto>, IUserPasswordStore<IdentityDto>, IUserLoginStore<IdentityDto>,
        IUserEmailStore<IdentityDto>, IUserAuthenticatorKeyStore<IdentityDto>
       , IUserLockoutStore<IdentityDto>
    {
        #region Declarations

        private readonly IMediator _mediator;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private const string InternalLoginProvider = "[AspNetUserStore]";
        private const string AuthenticatorKeyTokenName = "AuthenticatorKey";
        //private const string RecoveryCodeTokenName = "RecoveryCodes";

        #endregion

        #region Public Constructor

        public UserStore(IMediator mediator, IHttpContextAccessor httpContextAccessor)
        {
            _mediator = mediator;
            _httpContextAccessor = httpContextAccessor;
        }

        #endregion

        #region Identity Methods

        /// <summary>
        /// Gets details about an Identity record
        /// </summary>
        /// <param name="id">The Identity Id</param>
        /// <param name="cancellationToken"></param>
        /// <returns>The Identity object</returns>
        public async Task<IdentityDto> FindByIdAsync(string id, CancellationToken cancellationToken)
        {
            if (Guid.Parse(id) == Guid.Empty)
            {
                DeleteAllCookies();
                _httpContextAccessor.HttpContext.Response.Redirect("~/");
                return null;
            }

            return await Task.Run(() => GetIdentityByFilterCretireia("IdentityId", new Guid(id)));
        }

        public async Task<IdentityDto> FindByNameAsync(string normalizedUserName, CancellationToken cancellationToken)
        {

            return await Task.Run(() => GetIdentityByFilterCretireia("UserName", normalizedUserName));
        }


        public async Task<IdentityDto> FindByUsername(string userName, CancellationToken cancellationToken)
        {
            return await Task.Run(() => GetIdentityByFilterCretireia("UserName", userName));
        }

        public Task<string> GetUserIdAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Id.ToString());
        }

        public Task<string> GetUserNameAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Username);
        }

        public Task SetNormalizedUserNameAsync(IdentityDto user, string normalizedName, CancellationToken cancellationToken)
        {
            user.Username = normalizedName;
            return Task.FromResult(0);
        }

        public async Task<string> GetPasswordHashAsync(IdentityDto user, CancellationToken cancellationToken)
        {

            return await Task.FromResult(user.PasswordHash);
        }

        public Task<bool> HasPasswordAsync(IdentityDto user, CancellationToken cancellationToken)
        {

            return Task.FromResult(user != null);
        }

        public async Task<IdentityDto> FindByLoginAsync(string loginProvider, string email, CancellationToken cancellationToken)
        {

            return await Task.Run(() => GetIdentityByFilterCretireia("Email", email));
        }

        public Task SetPasswordHashAsync(IdentityDto user, string passwordHash, CancellationToken cancellationToken)
        {
            user.PasswordHash = passwordHash;
            return Task.FromResult(0);
        }

        #endregion

        #region UserRole Methods

        /// <summary>
        /// Invoked by ASP.NET Identity f/w to retrieve a list of roles assigned to a user
        /// We always return an empty list because roles are not assigned to the identity that is signing in, but a User linked to the Identity.
        /// The Functional and Data roles associated with the logged in User are populated via a CustomClaims Factory <see cref="T:CustomClaimsPrincipalFactory"/>
        /// </summary>
        /// <param name="identity">The Identity whose roles need to be retrieved</param>
        /// <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> used to propagate notifications that the operation should be canceled</param>
        /// <returns>An empty list</returns>
        public async Task<IList<string>> GetRolesAsync(IdentityDto identity, CancellationToken cancellationToken)
        {
            return new List<string>();
        }

        #endregion

        #region No Implementation Interface Methods

        public Task<IdentityResult> DeleteAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            throw new NotImplementedException();
        }

        public Task SetUserNameAsync(IdentityDto user, string userName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<IdentityResult> UpdateAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(IdentityResult.Success);
        }

        public Task<string> GetNormalizedUserNameAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task AddLoginAsync(IdentityDto user, UserLoginInfo login, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }
        public Task RemoveLoginAsync(IdentityDto user, string loginProvider, string providerKey, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<IList<UserLoginInfo>> GetLoginsAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task AddToRoleAsync(IdentityDto user, string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task RemoveFromRoleAsync(IdentityDto user, string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<bool> IsInRoleAsync(IdentityDto user, string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<IList<IdentityDto>> GetUsersInRoleAsync(string roleName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// No user is required to be created from the System. 
        /// </summary>
        /// <param name="user"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public Task<IdentityResult> CreateAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();

            //return Task.FromResult(IdentityResult.Success);
        }

        #endregion

        #region Dispose

        public void Dispose()
        {
            // Nothing to dispose.
        }

        #endregion

        #region IUserEmailStore

        public Task SetEmailAsync(IdentityDto user, string email, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<string> GetEmailAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Email);
        }

        public Task<bool> GetEmailConfirmedAsync(IdentityDto user, CancellationToken cancellationToken)
        {

            return Task.FromResult(true);
        }

        public Task SetEmailConfirmedAsync(IdentityDto user, bool confirmed, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<IdentityDto> FindByEmailAsync(string normalizedEmail, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<string> GetNormalizedEmailAsync(IdentityDto user, CancellationToken cancellationToken)
        {

            return Task.FromResult(user.Email.ToLower());
        }

        public Task SetNormalizedEmailAsync(IdentityDto user, string normalizedEmail, CancellationToken cancellationToken)
        {

            user.Email = normalizedEmail;
            return Task.FromResult(0);
        }

        #endregion

        #region IUserAuthenticatorKeyStore

        /// <summary>
        /// Sets the authenticator key for the specified <paramref name="user"/>.
        /// </summary>
        /// <param name="user">The user whose authenticator key should be set.</param>
        /// <param name="key">The authenticator key to set.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
        /// <returns>The <see cref="Task"/> that represents the asynchronous operation.</returns>
        public async Task SetAuthenticatorKeyAsync(IdentityDto user, string key, CancellationToken cancellationToken)
        {
            await SetTokenAsync(user, InternalLoginProvider, AuthenticatorKeyTokenName, key);
        }

        /// <summary>
        /// Get the authenticator key for the specified <paramref name="user" />.
        /// </summary>
        /// <param name="user">The user whose security stamp should be set.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
        /// <returns>The <see cref="Task"/> that represents the asynchronous operation, containing the security stamp for the specified <paramref name="user"/>.</returns>
        public Task<string> GetAuthenticatorKeyAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.AuthenticatorKey);
        }

        /// <summary>
        /// Sets the token value for a particular user.
        /// </summary>
        /// <param name="user">The user.</param>
        /// <param name="loginProvider">The authentication provider for the token.</param>
        /// <param name="name">The name of the token.</param>
        /// <param name="value">The value of the token.</param>
        /// <returns>The <see cref="Task"/> that represents the asynchronous operation.</returns>
        private async Task SetTokenAsync(IdentityDto user, string loginProvider, string name, string value)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));

            // Update the authentication key at the time of Initial Authenticator App setup and Resetting the authenticator app.
            if (!string.IsNullOrEmpty(value))
            {
                await CreateUserTokenAsync(user, loginProvider, name, value);

                // _userAndRoleService.UpdateAuthenticationKeyForIdentity(user.Id, value);
            }
        }

        /// <summary>
        /// Called to create a new instance of a <see cref="IdentityUserToken{TKey}"/>.
        /// </summary>
        /// <param name="user">The associated user.</param>
        /// <param name="loginProvider">The associated login provider.</param>
        /// <param name="name">The name of the user token.</param>
        /// <param name="value">The value of the user token.</param>
        /// <returns></returns>
        private Task<IdentityUserToken<Guid>> CreateUserTokenAsync(IdentityDto user, string loginProvider, string name, string value)
        {
            user.AuthenticatorKey = value;
            user.TwoFactorEnabled = false;

            IdentityUserToken<Guid> token = new IdentityUserToken<Guid>
            {
                UserId = user.Id,
                LoginProvider = loginProvider,
                Name = name,
                Value = value
            };

            return Task.FromResult(token);
        }

        #endregion

        #region IUserLockoutStore

        public Task<DateTimeOffset?> GetLockoutEndDateAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task SetLockoutEndDateAsync(IdentityDto user, DateTimeOffset? lockoutEnd, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// Increment the Access Failed Count for a User on unsuccessful log-in attempt
        /// </summary>
        /// <param name="user">The user whose cancellation count should be incremented.</param>
        /// <param name="cancellationToken">The <see cref="CancellationToken"/> used to propagate notifications that the operation should be canceled.</param>
        /// <returns>The <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation, containing the incremented failed access count.</returns>
        public Task<int> IncrementAccessFailedCountAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // _userAndRoleService.UpdateAccessFailureCountForIdentity(user.Id, user.AccessFailedCount, false);
            return Task.FromResult(0);
        }

        /// <summary>
		/// Resets a user's failed access count.
		/// </summary>
		/// <param name="user">The user whose failed access count should be reset.</param>
		/// <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> used to propagate notifications that the operation should be canceled.</param>
		/// <returns>The <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation.</returns>
		/// <remarks>This is typically called after the account is successfully accessed.</remarks>
        public Task ResetAccessFailedCountAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // _userAndRoleService.UpdateAccessFailureCountForIdentity(user.Id, user.AccessFailedCount, true);
            return Task.FromResult(0);
        }

        /// <summary>
		/// Retrieves the current failed access count for the specified <paramref name="user" />.
		/// </summary>
		/// <param name="user">The user whose failed access count should be retrieved.</param>
		/// <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> used to propagate notifications that the operation should be canceled.</param>
		/// <returns>The <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation, containing the failed access count.</returns>
        public Task<int> GetAccessFailedCountAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.AccessFailedCount);
        }

        /// <summary>
        /// Retrieves a flag indicating whether user lockout can enabled for the specified user.
        /// </summary>
        /// <param name="user">The user whose ability to be locked out should be returned.</param>
        /// <param name="cancellationToken">The <see cref="T:System.Threading.CancellationToken" /> used to propagate notifications that the operation should be canceled.</param>
        /// <returns>
        /// The <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous operation, true if a user can be locked out, otherwise false.
        /// </returns>
        public Task<bool> GetLockoutEnabledAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            bool isLockeout = false; //_userAndRoleService.GetLockoutEnabledForIdentity(user.Id)

            return Task.FromResult(isLockeout);
        }

        public Task SetLockoutEnabledAsync(IdentityDto user, bool enabled, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        #endregion

        #region private region

        /// <summary>
        /// Delete all cookies for a user after log out.
        /// </summary>
        private void DeleteAllCookies()
        {
            _httpContextAccessor.HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            _httpContextAccessor.HttpContext.SignOutAsync();
            foreach (var cookie in _httpContextAccessor.HttpContext.Request.Cookies)
            {
                _httpContextAccessor.HttpContext.Response.Cookies.Delete(cookie.Key);
            }
        }

        private async Task<IdentityDto> GetIdentityByFilterCretireia(string field, dynamic value)
        {

            List<IdentityDto> identities = await _mediator.Send(new GetIdentityQuery(new List<FilterCriteria>()
            {
                new FilterCriteria()
                {
                    Field = field,
                    Operator = "=",
                    Value = value
                }
            }));

            return identities.FirstOrDefault();
        }

        #endregion

    }
}
