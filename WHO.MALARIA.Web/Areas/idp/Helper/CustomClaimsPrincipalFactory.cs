﻿//used for avoiding ambiguous name for constants in web project and model project 
using ClaimConstants = WHO.MALARIA.Domain.Constants.Constants.IdentityClaims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using WHO.MALARIA.Domain.Dtos;
using static WHO.MALARIA.Domain.Constants.Constants;

namespace WHO.MALARIA.Web.Areas.Idp.Helper
{
    /// <summary>
    /// Provides methods to create a claims principal for a given user.
    /// </summary>
    public class CustomClaimsPrincipalFactory : UserClaimsPrincipalFactory<IdentityDto, UserRoleDto>
    {

        public CustomClaimsPrincipalFactory(UserManager<IdentityDto> userManager, RoleManager<UserRoleDto> roleManager,
            IOptions<IdentityOptions> options) : base(userManager, roleManager, options)
        {
        }

        /// <summary>
        /// Creates a <see cref="T:System.Security.Claims.ClaimsPrincipal" /> from an user asynchronously.
        /// </summary>
        /// <param name="identity">The Identity to create a <see cref="T:System.Security.Claims.ClaimsPrincipal" /> from.</param>
        /// <returns>The <see cref="T:System.Threading.Tasks.Task" /> that represents the asynchronous creation operation, containing the created <see cref="T:System.Security.Claims.ClaimsPrincipal" />.</returns>
        public override async Task<ClaimsPrincipal> CreateAsync(IdentityDto identity)
        {
            //Add default claims
            ClaimsPrincipal principal = await base.CreateAsync(identity);

            //Check if a valid non-null user record is populated with the Identity
            UserDto user = identity.User;
            if (user != null)
            {
                //Assign the UserId to the SID claim
                ClaimsIdentity claimsIdentity = ((ClaimsIdentity)principal.Identity);
                claimsIdentity.AddClaim(new Claim(ClaimTypes.PrimarySid, user.Id.ToString()));

                claimsIdentity.AddClaim(new Claim(ClaimConstants.UsernameClaim, identity.Username));
                claimsIdentity.AddClaim(new Claim(ClaimConstants.UserType, user.UserType.ToString()));

                if (identity.RequestedCountryIds != null && identity.RequestedCountryIds.Length > 0)
                {
                    claimsIdentity.AddClaim(new Claim(ClaimConstants.CountriesRequested, string.Join(",", identity.RequestedCountryIds)));
                }

            }

            return principal;
        }
    }
}
