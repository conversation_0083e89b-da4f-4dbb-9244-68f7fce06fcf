﻿
using Duende.IdentityServer.Stores;
using Microsoft.Extensions.DependencyInjection;

namespace WHO.MALARIA.Web.Areas.Idp.Helper
{
    /// <summary>
    ///  Class responsible for validating the clients registered in STS.
    /// </summary>
    public static class CustomIdentityServerBuilderExtensions
    {
        public static IIdentityServerBuilder AddCustomUserStore(this IIdentityServerBuilder builder)
        {
            builder.Services.AddScoped<IClientStore, CustomClientStore>();
            builder.AddClientStore<CustomClientStore>();

            return builder;
        }
    }
}
